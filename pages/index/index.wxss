/**pages/index/index.wxss**/

/* 未登录状态 */
.login-section {
  padding: 80rpx 48rpx;
  text-align: center;
}

.welcome-header {
  margin-bottom: 120rpx;
}

.coffee-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.welcome-title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 16rpx;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 48rpx;
}

.features {
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  padding: 32rpx;
  background-color: var(--card-background);
  border-radius: 24rpx;
  box-shadow: var(--shadow);
}

.feature-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.feature-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
}

.login-btn-area {
  margin-top: 48rpx;
}

.login-btn {
  width: 100%;
  font-size: 36rpx;
  padding: 32rpx;
}

/* 已登录状态 */
.dashboard {
  padding: 32rpx;
}

.user-header {
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 24rpx;
  box-shadow: var(--shadow);
  margin-bottom: 32rpx;
  text-align: center;
}

.user-greeting {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12rpx;
}

.greeting-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 8rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.user-welcome {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* 统计数据 */
.stats-section {
  margin-bottom: 32rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.stats-grid {
  display: flex;
  gap: 16rpx;
}

.stat-item {
  flex: 1;
  background-color: var(--card-background);
  border-radius: 20rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
  background-color: var(--background-color);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 最近添加的咖啡 */
.recent-coffees {
  margin-bottom: 32rpx;
}

.recent-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.recent-loading {
  text-align: center;
  padding: 80rpx 32rpx;
  color: var(--text-light);
  font-size: 28rpx;
}

.recent-empty {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: var(--text-light);
}

.recent-list {
  padding: 0 16rpx;
}

.recent-coffee-item {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.recent-coffee-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(139, 69, 19, 0.15);
}

.coffee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.coffee-brand {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.coffee-price {
  font-size: 32rpx;
  color: var(--primary-color);
  font-weight: 600;
}

.coffee-name {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.coffee-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.info-item {
  font-size: 24rpx;
  color: var(--text-light);
  background-color: var(--background-color);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid var(--border-color);
}

.coffee-flavor {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.coffee-rating {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  opacity: 0.3;
}

.star.active {
  opacity: 1;
}

/* 快捷添加部分 */
.quick-add-section {
  margin-bottom: 32rpx;
}

.quick-add-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.quick-add-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(139, 69, 19, 0.2);
}

.add-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.add-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background-color: var(--card-background);
  border-radius: 24rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.detail-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-light);
}

.detail-body {
  flex: 1;
  padding: 32rpx;
}

.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
}

.label {
  font-size: 28rpx;
  color: var(--text-secondary);
  width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: var(--text-primary);
  flex: 1;
}

.flavor-text,
.review-text,
.grind-text,
.link-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

.rating-display {
  display: flex;
  gap: 4rpx;
  margin-bottom: 16rpx;
}
