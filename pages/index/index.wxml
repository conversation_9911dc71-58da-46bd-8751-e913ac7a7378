<!--pages/index/index.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <view wx:if="{{!isLoggedIn}}" class="login-section">
    <view class="welcome-header">
      <view class="coffee-icon">☕</view>
      <view class="welcome-title">欢迎来到我的咖啡</view>
      <view class="welcome-subtitle">专业的咖啡豆管理工具</view>
    </view>

    <view class="features">
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">咖啡图谱管理</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🏭</view>
        <view class="feature-text">豆仓存储记录</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📊</view>
        <view class="feature-text">数据统计分析</view>
      </view>
    </view>

    <view class="login-btn-area">
      <button class="btn login-btn" bindtap="initPage" loading="{{loading}}">
        登录开始使用
      </button>
    </view>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="dashboard">
    <!-- 用户信息 -->
    <view class="user-header">
      <view class="user-greeting">
        <view class="greeting-text">你好，</view>
        <view class="user-name">{{userInfo.nickName}}</view>
      </view>
      <view class="user-welcome">开始你的咖啡之旅吧！</view>
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-title">我的数据</view>
      <view class="stats-grid">
        <view class="stat-item" bindtap="navigateToAtlas">
          <view class="stat-number">{{stats.totalCoffees}}</view>
          <view class="stat-label">咖啡图谱</view>
        </view>
        <view class="stat-item" bindtap="navigateToStorage">
          <view class="stat-number">{{stats.totalStorage}}</view>
          <view class="stat-label">豆仓存储</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{stats.recentActivity}}</view>
          <view class="stat-label">近期活动</view>
        </view>
      </view>
    </view>

    <!-- 快捷添加 -->
    <view class="quick-add-section">
      <button class="quick-add-btn" bindtap="navigateToAddCoffee">
        <view class="add-icon">➕</view>
        <view class="add-text">添加咖啡豆</view>
      </button>
    </view>

    <!-- 最近添加的咖啡 -->
    <view class="recent-coffees">
      <view class="recent-title">最近添加</view>

      <!-- 加载状态 -->
      <view wx:if="{{recentLoading}}" class="recent-loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <view wx:elif="{{recentCoffees.length === 0}}" class="recent-empty">
        <view class="empty-icon">☕</view>
        <view class="empty-text">还没有咖啡记录</view>
        <view class="empty-hint">点击上方按钮添加你的第一个咖啡</view>
      </view>

      <!-- 咖啡列表 -->
      <view wx:else class="recent-list">
        <view
          wx:for="{{recentCoffees}}"
          wx:key="_id"
          class="recent-coffee-item"
          data-coffee="{{item}}"
          bindtap="viewCoffeeDetail"
        >
          <view class="coffee-header">
            <view class="coffee-brand">{{item.brand}}</view>
            <view class="coffee-price">¥{{item.price}}</view>
          </view>
          <view class="coffee-name">{{item.name}}</view>
          <view class="coffee-info">
            <text class="info-item">{{item.origin}}</text>
            <text class="info-item">{{item.variety}}</text>
            <text class="info-item">{{item.roastLevel}}</text>
          </view>
          <view class="coffee-flavor">{{item.flavor}}</view>
          <view class="coffee-rating">
            <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{item.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 咖啡详情弹窗 -->
  <view wx:if="{{showDetail}}" class="detail-modal" bindtap="closeDetail">
    <view class="detail-content" catchtap="">
      <view class="detail-header">
        <view class="detail-title">{{selectedCoffee.name}}</view>
        <view class="detail-close" bindtap="closeDetail">✕</view>
      </view>

      <scroll-view class="detail-body" scroll-y>
        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="info-row">
            <text class="label">品牌：</text>
            <text class="value">{{selectedCoffee.brand}}</text>
          </view>
          <view class="info-row">
            <text class="label">产地：</text>
            <text class="value">{{selectedCoffee.origin}}</text>
          </view>
          <view class="info-row">
            <text class="label">品种：</text>
            <text class="value">{{selectedCoffee.variety}}</text>
          </view>
          <view class="info-row">
            <text class="label">处理法：</text>
            <text class="value">{{selectedCoffee.process}}</text>
          </view>
          <view class="info-row">
            <text class="label">烘焙度：</text>
            <text class="value">{{selectedCoffee.roastLevel}}</text>
          </view>
          <view class="info-row">
            <text class="label">价格：</text>
            <text class="value">¥{{selectedCoffee.price}}</text>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">风味描述</view>
          <view class="flavor-text">{{selectedCoffee.flavor}}</view>
        </view>

        <view class="detail-section">
          <view class="section-title">个人评价</view>
          <view class="rating-display">
            <text wx:for="{{[1,2,3,4,5]}}" wx:for-index="starIndex" wx:key="*this" class="star {{selectedCoffee.rating >= starIndex + 1 ? 'active' : ''}}">⭐</text>
          </view>
          <view class="review-text">{{selectedCoffee.review}}</view>
        </view>

        <view wx:if="{{selectedCoffee.grindSize}}" class="detail-section">
          <view class="section-title">推荐研磨度</view>
          <view class="grind-text">{{selectedCoffee.grindSize}}</view>
        </view>

        <view wx:if="{{selectedCoffee.purchaseLink}}" class="detail-section">
          <view class="section-title">购买链接</view>
          <view class="link-text">{{selectedCoffee.purchaseLink}}</view>
        </view>
      </scroll-view>
    </view>
  </view>

</view>
